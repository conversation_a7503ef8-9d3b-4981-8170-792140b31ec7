import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:developer' as developer;

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Firebase Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const FirebaseTestPage(),
    );
  }
}

class FirebaseTestPage extends StatefulWidget {
  const FirebaseTestPage({super.key});

  @override
  State<FirebaseTestPage> createState() => _FirebaseTestPageState();
}

class _FirebaseTestPageState extends State<FirebaseTestPage> {
  String _status = 'Checking Firebase connection...';
  bool _isLoading = true;
  bool _isConnected = false;
  List<String> _collections = [];

  @override
  void initState() {
    super.initState();
    _checkFirebaseConnection();
  }

  Future<void> _checkFirebaseConnection() async {
    try {
      // Try to access Firestore
      final firestore = FirebaseFirestore.instance;

      // Define known collection names to check
      final knownCollections = [
        'products',
        'users',
        'orders',
        'earnings',
        'withdrawals',
        'test_collection'
      ];

      // Check which collections exist by trying to get a document from each
      List<String> existingCollections = [];

      for (final collectionName in knownCollections) {
        try {
          // Try to get a single document to check if collection exists
          final snapshot =
              await firestore.collection(collectionName).limit(1).get();
          // If we get here without error, the collection exists (even if empty)
          existingCollections.add(collectionName);
          developer.log(
              'Collection found: $collectionName (${snapshot.docs.isEmpty ? "empty" : "has data"})');
        } catch (e) {
          // Skip collections that don't exist or have permission issues
          developer.log('Collection not accessible: $collectionName - $e');
        }
      }

      developer.log('Firebase connection successful');
      developer.log('Collections: $existingCollections');

      setState(() {
        _status = 'Firebase connection successful!';
        _isLoading = false;
        _isConnected = true;
        _collections = existingCollections;
      });
    } catch (e) {
      developer.log('Firebase connection error: $e');
      setState(() {
        _status = 'Firebase connection error: $e';
        _isLoading = false;
        _isConnected = false;
      });
    }
  }

  Future<void> _createTestCollection() async {
    setState(() {
      _isLoading = true;
      _status = 'Creating test collection...';
    });

    try {
      final firestore = FirebaseFirestore.instance;

      // Create a test collection with a document
      await firestore.collection('test_collection').add({
        'created_at': Timestamp.now(),
        'test': true,
        'message': 'This is a test document',
      });

      // Refresh collections
      await _checkFirebaseConnection();

      setState(() {
        _status = 'Test collection created successfully!';
      });
    } catch (e) {
      developer.log('Error creating test collection: $e');
      setState(() {
        _status = 'Error creating test collection: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase Test'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (_isLoading)
                const CircularProgressIndicator()
              else
                Icon(
                  _isConnected ? Icons.check_circle : Icons.error,
                  color: _isConnected ? Colors.green : Colors.red,
                  size: 48.0,
                ),
              const SizedBox(height: 16.0),
              Text(
                _status,
                style: TextStyle(
                  color: _isConnected ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24.0),
              if (_isConnected) ...[
                const Text(
                  'Collections:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18.0,
                  ),
                ),
                const SizedBox(height: 8.0),
                if (_collections.isEmpty)
                  const Text('No collections found')
                else
                  Column(
                    children: _collections
                        .map((collection) => Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 4.0),
                              child: Text(collection),
                            ))
                        .toList(),
                  ),
                const SizedBox(height: 24.0),
                ElevatedButton(
                  onPressed: _createTestCollection,
                  child: const Text('Create Test Collection'),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
