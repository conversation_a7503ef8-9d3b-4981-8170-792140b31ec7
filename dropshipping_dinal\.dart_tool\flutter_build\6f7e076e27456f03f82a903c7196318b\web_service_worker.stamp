{"inputs": ["build\\web\\assets\\AssetManifest.bin", "build\\web\\assets\\AssetManifest.bin.json", "build\\web\\assets\\AssetManifest.json", "build\\web\\assets\\assets\\audios\\favicon.png", "build\\web\\assets\\assets\\fonts\\favicon.png", "build\\web\\assets\\assets\\images\\favicon.png", "build\\web\\assets\\assets\\jsons\\favicon.png", "build\\web\\assets\\assets\\pdfs\\favicon.png", "build\\web\\assets\\assets\\rive_animations\\favicon.png", "build\\web\\assets\\assets\\videos\\favicon.png", "build\\web\\assets\\FontManifest.json", "build\\web\\assets\\fonts\\MaterialIcons-Regular.otf", "build\\web\\assets\\NOTICES", "build\\web\\assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "build\\web\\assets\\packages\\font_awesome_flutter\\lib\\fonts\\fa-brands-400.ttf", "build\\web\\assets\\packages\\font_awesome_flutter\\lib\\fonts\\fa-regular-400.ttf", "build\\web\\assets\\packages\\font_awesome_flutter\\lib\\fonts\\fa-solid-900.ttf", "build\\web\\assets\\shaders\\ink_sparkle.frag", "build\\web\\canvaskit\\canvaskit.js", "build\\web\\canvaskit\\canvaskit.js.symbols", "build\\web\\canvaskit\\canvaskit.wasm", "build\\web\\canvaskit\\chromium\\canvaskit.js", "build\\web\\canvaskit\\chromium\\canvaskit.js.symbols", "build\\web\\canvaskit\\chromium\\canvaskit.wasm", "build\\web\\canvaskit\\skwasm.js", "build\\web\\canvaskit\\skwasm.js.symbols", "build\\web\\canvaskit\\skwasm.wasm", "build\\web\\canvaskit\\skwasm_st.js", "build\\web\\canvaskit\\skwasm_st.js.symbols", "build\\web\\canvaskit\\skwasm_st.wasm", "build\\web\\favicon.png", "build\\web\\flutter.js", "build\\web\\flutter_bootstrap.js", "build\\web\\icons\\Icon-192.png", "build\\web\\icons\\Icon-512.png", "build\\web\\index.html", "build\\web\\main.dart.js", "build\\web\\version.json"], "outputs": ["build\\web\\flutter_service_worker.js"]}