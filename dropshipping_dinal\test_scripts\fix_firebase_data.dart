import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'lib/firebase_options.dart';

/// Script to fix existing Firebase data structure issues
void main() async {
  print('🔧 Starting Firebase data fix...\n');

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  final firestore = FirebaseFirestore.instance;

  try {
    // Fix products collection
    await fixProductsCollection(firestore);

    print('\n✅ Firebase data fix completed successfully!');
    print('\n📋 Summary of fixes:');
    print('✅ Products: Fixed imageUrl arrays and categories');
    print('✅ Categories: Changed "non" to "General"');
    print('✅ Image URLs: Converted arrays to strings');
    print('✅ Added missing primaryCategory fields');
  } catch (e) {
    print('❌ Error fixing Firebase data: $e');
  }
}

/// Fix products collection data structure
Future<void> fixProductsCollection(FirebaseFirestore firestore) async {
  print('📦 Fixing products collection...');

  final productsCollection = firestore.collection('products');
  final snapshot = await productsCollection.get();

  int fixedCount = 0;

  for (final doc in snapshot.docs) {
    final data = doc.data();
    final updates = <String, dynamic>{};
    bool needsUpdate = false;

    // Fix imageUrl if it's an array
    if (data['imageUrl'] is List) {
      final imageUrlList = data['imageUrl'] as List;
      if (imageUrlList.isNotEmpty) {
        updates['imageUrl'] = imageUrlList.first.toString();
        needsUpdate = true;
        print('  📸 Fixed imageUrl for product: ${data['name']}');
      }
    }

    // Fix categories if they contain "non"
    if (data['categories'] is List) {
      final categories = List<String>.from(data['categories']);
      if (categories.contains('non') || categories.isEmpty) {
        updates['categories'] = ['General'];
        updates['category'] = 'General';
        needsUpdate = true;
        print('  🏷️ Fixed categories for product: ${data['name']}');
      }
    }

    // Fix category field if it's "non"
    if (data['category'] == 'non' ||
        data['category'] == null ||
        data['category'] == '') {
      updates['category'] = 'General';
      needsUpdate = true;
      print('  🏷️ Fixed category field for product: ${data['name']}');
    }

    // Add primaryCategory field if missing
    if (!data.containsKey('primaryCategory')) {
      final category = updates['category'] ?? data['category'] ?? 'General';
      updates['primaryCategory'] = category;
      needsUpdate = true;
      print('  ➕ Added primaryCategory for product: ${data['name']}');
    }

    // Ensure imageUrls array exists
    if (!data.containsKey('imageUrls') || data['imageUrls'] == null) {
      final imageUrl = updates['imageUrl'] ?? data['imageUrl'] ?? '';
      if (imageUrl.isNotEmpty) {
        updates['imageUrls'] = [imageUrl];
        needsUpdate = true;
        print('  📸 Added imageUrls array for product: ${data['name']}');
      }
    }

    // Add updatedAt if missing
    if (!data.containsKey('updatedAt')) {
      updates['updatedAt'] = FieldValue.serverTimestamp();
      needsUpdate = true;
    }

    // Apply updates if needed
    if (needsUpdate) {
      await doc.reference.update(updates);
      fixedCount++;
      print('  ✅ Updated product: ${data['name']}');
    }
  }

  print('📦 Products collection: Fixed $fixedCount products');
}
