import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';

/// Quick Firebase Diagnostic Script
/// This script will quickly check your Firebase setup and identify issues

void main() async {
  print('🔥 FIREBASE QUICK DIAGNOSTIC STARTING...\n');

  try {
    // Initialize Firebase (will use existing configuration if already initialized)
    await Firebase.initializeApp();
    print('✅ Firebase initialized successfully\n');

    final diagnostic = QuickFirebaseDiagnostic();
    await diagnostic.runQuickAnalysis();
  } catch (e) {
    print('❌ Failed to initialize Firebase: $e');
    print('🔍 This might be why orders aren\'t saving!');
    exit(1);
  }
}

class QuickFirebaseDiagnostic {
  final FirebaseFirestore firestore = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  /// Run quick Firebase analysis
  Future<void> runQuickAnalysis() async {
    print('🚀 QUICK FIREBASE ANALYSIS');
    print('=' * 50);

    // 1. Check authentication
    await _checkAuthentication();

    // 2. Check collections
    await _checkCollections();

    // 3. Test order creation
    await _testOrderCreation();

    // 4. Show recommendations
    _showRecommendations();

    print('\n${'=' * 50}');
    print('🎉 QUICK ANALYSIS COMPLETE');
    print('=' * 50);
  }

  /// Check authentication status
  Future<void> _checkAuthentication() async {
    print('\n👤 AUTHENTICATION STATUS');
    print('-' * 30);

    final currentUser = auth.currentUser;

    if (currentUser != null) {
      print('✅ User authenticated: ${currentUser.uid}');
      print('📧 Email: ${currentUser.email ?? 'No email'}');
      print('✉️ Email verified: ${currentUser.emailVerified}');
    } else {
      print('❌ NO USER AUTHENTICATED');
      print('🔍 This is likely why orders aren\'t saving!');
      print('💡 Solution: Sign in to your app first, then place orders');
    }
  }

  /// Check collections
  Future<void> _checkCollections() async {
    print('\n📊 COLLECTION STATUS');
    print('-' * 30);

    final collections = [
      'products',
      'orders',
      'users',
      'earnings',
      'userBalances'
    ];

    for (final collectionName in collections) {
      try {
        final snapshot = await firestore.collection(collectionName).get();
        print('📁 $collectionName: ${snapshot.docs.length} documents');

        if (collectionName == 'orders' && snapshot.docs.isNotEmpty) {
          _analyzeOrders(snapshot.docs);
        }
      } catch (e) {
        print('❌ $collectionName: Error - $e');
      }
    }
  }

  /// Test order creation
  Future<void> _testOrderCreation() async {
    print('\n🧪 ORDER CREATION TEST');
    print('-' * 30);

    if (auth.currentUser == null) {
      print('❌ Cannot test - no authenticated user');
      print('💡 Sign in first to test order creation');
      return;
    }

    try {
      // Test order data
      final testOrderData = {
        'userId': auth.currentUser!.uid,
        'userName': 'Test User',
        'userPhone': '1234567890',
        'address': 'Test Address',
        'city': 'Test City',
        'items': [
          {
            'productId': 'test_product',
            'productName': 'Test Product',
            'productImage': 'test.jpg',
            'price': 100.0,
            'mainPrice': 80.0,
            'quantity': 1,
            'color': 'Red',
          }
        ],
        'totalAmount': 100.0,
        'totalEarnings': 20.0,
        'status': 'pending',
        'earningsConfirmed': false,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // Try to create order
      final orderRef = await firestore.collection('orders').add(testOrderData);
      print('✅ Order creation: SUCCESS');
      print('📝 Test order ID: ${orderRef.id}');

      // Test earnings creation
      await _testEarningsCreation(orderRef.id, 20.0);

      // Clean up test order
      await orderRef.delete();
      print('🧹 Test order cleaned up');
    } catch (e) {
      print('❌ Order creation: FAILED');
      print('🔍 Error: $e');
      print('💡 This explains why orders aren\'t saving in your app!');
    }
  }

  /// Test earnings creation
  Future<void> _testEarningsCreation(String orderId, double amount) async {
    try {
      // Test earnings record
      final earningsData = {
        'userId': auth.currentUser!.uid,
        'orderId': orderId,
        'amount': amount,
        'status': 'pending',
        'orderStatus': 'pending',
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      final earningsRef =
          await firestore.collection('earnings').add(earningsData);
      print('✅ Earnings creation: SUCCESS');

      // Test user balance update
      final balanceRef =
          firestore.collection('userBalances').doc(auth.currentUser!.uid);
      await balanceRef.set({
        'userId': auth.currentUser!.uid,
        'availableBalance': 0.0,
        'incomingEarnings': FieldValue.increment(amount),
        'totalEarnings': FieldValue.increment(amount),
        'pendingWithdrawals': 0.0,
        'totalWithdrawn': 0.0,
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      print('✅ Balance update: SUCCESS');

      // Clean up
      await earningsRef.delete();
    } catch (e) {
      print('❌ Earnings creation: FAILED - $e');
    }
  }

  /// Analyze existing orders
  void _analyzeOrders(List<QueryDocumentSnapshot> orders) {
    if (orders.isEmpty) return;

    print('   📋 Sample order fields:');
    final sampleOrder = orders.first.data() as Map<String, dynamic>;
    final fields = sampleOrder.keys.toList()..sort();

    for (final field in fields.take(5)) {
      print('      • $field: ${sampleOrder[field]}');
    }

    if (fields.length > 5) {
      print('      • ... and ${fields.length - 5} more fields');
    }
  }

  /// Show recommendations
  void _showRecommendations() {
    print('\n💡 RECOMMENDATIONS');
    print('-' * 30);

    if (auth.currentUser == null) {
      print('🔑 CRITICAL: Sign in to your app first');
      print('   • Go to your Flutter app');
      print('   • Sign in with email/password');
      print('   • Then try placing orders');
      print('');
    }

    print('📱 TO FIX ORDER SAVING:');
    print('   1. Make sure you\'re signed in');
    print('   2. Check Firebase security rules');
    print('   3. Verify internet connection');
    print('   4. Check console for errors');
    print('');

    print('🔧 NEXT STEPS:');
    print('   1. Sign in to your Flutter app');
    print('   2. Try placing an order');
    print('   3. Check if it appears in orders page');
    print('   4. Check if earnings show up');
  }
}
