import 'dart:io';

/// COMPREHENSIVE SYSTEM TESTING SCRIPT
/// Tests all components after audit fixes
void main() async {
  print('🧪 COMPREHENSIVE SYSTEM TESTING STARTING...\n');

  final testResults = <String, bool>{};
  final issues = <String>[];

  try {
    // Test 1: Firebase Configuration
    print('🔥 TEST 1: Firebase Configuration');
    testResults['firebase_config'] = await testFirebaseConfiguration(issues);

    // Test 2: Flutter App Components
    print('\n📱 TEST 2: Flutter App Components');
    testResults['flutter_components'] = await testFlutterComponents(issues);

    // Test 3: Admin Panel Connectivity
    print('\n🖥️ TEST 3: Admin Panel Connectivity');
    testResults['admin_panel'] = await testAdminPanelConnectivity(issues);

    // Test 4: Service Integration
    print('\n🔧 TEST 4: Service Integration');
    testResults['service_integration'] = await testServiceIntegration(issues);

    // Test 5: Data Flow Testing
    print('\n🔄 TEST 5: Data Flow Testing');
    testResults['data_flow'] = await testDataFlow(issues);

    // Generate test report
    await generateTestReport(testResults, issues);

    print('\n🎉 COMPREHENSIVE TESTING COMPLETED!');
  } catch (e) {
    print('❌ Testing failed: $e');
  }
}

/// Test Firebase Configuration
Future<bool> testFirebaseConfiguration(List<String> issues) async {
  bool allPassed = true;

  // Check firebase.json
  final firebaseJson = File('firebase.json');
  if (await firebaseJson.exists()) {
    print('✅ firebase.json: Found');

    final content = await firebaseJson.readAsString();
    if (content.contains('firestore') && content.contains('rules')) {
      print('✅ firebase.json: Properly configured');
    } else {
      print('❌ firebase.json: Missing firestore configuration');
      issues.add('firebase.json missing firestore configuration');
      allPassed = false;
    }
  } else {
    print('❌ firebase.json: Missing');
    issues.add('firebase.json file missing');
    allPassed = false;
  }

  // Check firestore.rules
  final firestoreRules = File('firestore.rules');
  if (await firestoreRules.exists()) {
    print('✅ firestore.rules: Found');

    final content = await firestoreRules.readAsString();
    if (content.contains('allow read') && content.contains('allow write')) {
      print('✅ firestore.rules: Has read/write rules');
    } else {
      print('❌ firestore.rules: Missing basic rules');
      issues.add('firestore.rules missing basic read/write rules');
      allPassed = false;
    }
  } else {
    print('❌ firestore.rules: Missing');
    issues.add('firestore.rules file missing');
    allPassed = false;
  }

  // Check Firebase options
  final firebaseOptions = File('lib/firebase_options.dart');
  if (await firebaseOptions.exists()) {
    print('✅ firebase_options.dart: Found');
  } else {
    print('❌ firebase_options.dart: Missing');
    issues.add('firebase_options.dart file missing');
    allPassed = false;
  }

  return allPassed;
}

/// Test Flutter Components
Future<bool> testFlutterComponents(List<String> issues) async {
  bool allPassed = true;

  // Test critical services
  final services = [
    'lib/services/firebase_service.dart',
    'lib/services/cart_service.dart',
    'lib/services/balance_service.dart',
    'lib/services/product_service.dart'
  ];

  for (String servicePath in services) {
    final file = File(servicePath);
    if (await file.exists()) {
      final content = await file.readAsString();

      // Check for error handling
      if (content.contains('try') && content.contains('catch')) {
        print('✅ ${servicePath.split('/').last}: Has error handling');
      } else {
        print('❌ ${servicePath.split('/').last}: Missing error handling');
        issues.add('${servicePath.split('/').last} missing error handling');
        allPassed = false;
      }

      // Check for Firebase integration
      if (content.contains('Firebase') || content.contains('firestore')) {
        print('✅ ${servicePath.split('/').last}: Firebase integrated');
      } else {
        print('⚠️ ${servicePath.split('/').last}: No Firebase integration');
      }
    } else {
      print('❌ ${servicePath.split('/').last}: Missing');
      issues.add('Missing service: ${servicePath.split('/').last}');
      allPassed = false;
    }
  }

  // Test critical models
  final models = [
    'lib/models/product_model.dart',
    'lib/models/order_model.dart',
    'lib/models/user_model.dart'
  ];

  for (String modelPath in models) {
    final file = File(modelPath);
    if (await file.exists()) {
      final content = await file.readAsString();

      // Check for serialization
      if (content.contains('toMap') && content.contains('fromMap')) {
        print('✅ ${modelPath.split('/').last}: Has serialization');
      } else {
        print('❌ ${modelPath.split('/').last}: Missing serialization');
        issues.add('${modelPath.split('/').last} missing serialization');
        allPassed = false;
      }
    } else {
      print('❌ ${modelPath.split('/').last}: Missing');
      issues.add('Missing model: ${modelPath.split('/').last}');
      allPassed = false;
    }
  }

  return allPassed;
}

/// Test Admin Panel Connectivity
Future<bool> testAdminPanelConnectivity(List<String> issues) async {
  bool allPassed = true;

  // Test if admin panel is running
  try {
    final client = HttpClient();
    final request = await client.getUrl(Uri.parse('http://localhost:9002'));
    final response = await request.close();

    if (response.statusCode == 200) {
      print('✅ Admin panel: Accessible on port 9002');
    } else {
      print('❌ Admin panel: Status ${response.statusCode}');
      issues.add('Admin panel not accessible (status ${response.statusCode})');
      allPassed = false;
    }

    client.close();
  } catch (e) {
    print('❌ Admin panel: Not accessible - $e');
    issues.add('Admin panel not accessible: $e');
    allPassed = false;
  }

  // Test API endpoints
  final endpoints = ['/api/products', '/api/orders', '/api/users'];

  for (String endpoint in endpoints) {
    try {
      final client = HttpClient();
      final request =
          await client.getUrl(Uri.parse('http://localhost:9002$endpoint'));
      final response = await request.close();

      if (response.statusCode == 200 || response.statusCode == 404) {
        print('✅ API endpoint $endpoint: Responding');
      } else {
        print('❌ API endpoint $endpoint: Status ${response.statusCode}');
        issues.add('API endpoint $endpoint not working');
        allPassed = false;
      }

      client.close();
    } catch (e) {
      print('❌ API endpoint $endpoint: Error - $e');
      issues.add('API endpoint $endpoint error: $e');
      allPassed = false;
    }
  }

  return allPassed;
}

/// Test Service Integration
Future<bool> testServiceIntegration(List<String> issues) async {
  bool allPassed = true;

  // Test if services can be imported
  final serviceImports = [
    "import 'package:cloud_firestore/cloud_firestore.dart';",
    "import 'package:firebase_auth/firebase_auth.dart';",
    "import 'package:provider/provider.dart';"
  ];

  final pubspecFile = File('pubspec.yaml');
  if (await pubspecFile.exists()) {
    final content = await pubspecFile.readAsString();

    // Check for required dependencies
    final requiredDeps = [
      'firebase_core',
      'cloud_firestore',
      'firebase_auth',
      'provider'
    ];

    for (String dep in requiredDeps) {
      if (content.contains('$dep:')) {
        print('✅ Dependency $dep: Found');
      } else {
        print('❌ Dependency $dep: Missing');
        issues.add('Missing dependency: $dep');
        allPassed = false;
      }
    }
  } else {
    print('❌ pubspec.yaml: Missing');
    issues.add('pubspec.yaml file missing');
    allPassed = false;
  }

  return allPassed;
}

/// Test Data Flow
Future<bool> testDataFlow(List<String> issues) async {
  bool allPassed = true;

  // Test if main.dart exists and has proper setup
  final mainFile = File('lib/main.dart');
  if (await mainFile.exists()) {
    final content = await mainFile.readAsString();

    // Check for Firebase initialization (either direct or via service)
    if (content.contains('Firebase.initializeApp') ||
        content.contains('appInitializationService')) {
      print('✅ Firebase initialization: Found (via service)');
    } else {
      print('❌ Firebase initialization: Missing');
      issues.add('Firebase initialization missing');
      allPassed = false;
    }

    if (content.contains('MultiProvider') ||
        content.contains('ChangeNotifierProvider')) {
      print('✅ Provider setup: Found in main.dart');
    } else {
      print('❌ Provider setup: Missing in main.dart');
      issues.add('Provider setup missing in main.dart');
      allPassed = false;
    }
  } else {
    print('❌ main.dart: Missing');
    issues.add('main.dart file missing');
    allPassed = false;
  }

  return allPassed;
}

/// Generate test report
Future<void> generateTestReport(
    Map<String, bool> testResults, List<String> issues) async {
  print('\n📊 GENERATING TEST REPORT...\n');

  final report = StringBuffer();
  final timestamp = DateTime.now().toIso8601String();

  report.writeln('# 🧪 COMPREHENSIVE SYSTEM TEST REPORT');
  report.writeln('**Generated:** $timestamp');
  report.writeln(
      '**System:** Flutter Dropshipping App + Next.js Admin Panel + Firebase');
  report.writeln('');

  // Test Results Summary
  report.writeln('## 📋 TEST RESULTS SUMMARY');
  report.writeln('');

  int passedTests = 0;
  int totalTests = testResults.length;

  testResults.forEach((testName, passed) {
    final status = passed ? '✅ PASSED' : '❌ FAILED';
    report.writeln(
        '- **${testName.replaceAll('_', ' ').toUpperCase()}:** $status');
    if (passed) passedTests++;
  });

  report.writeln('');
  report.writeln('**Overall Score:** $passedTests/$totalTests tests passed');
  report.writeln('');

  // Issues Found
  report.writeln('## 🚨 ISSUES FOUND: ${issues.length}');
  if (issues.isNotEmpty) {
    for (int i = 0; i < issues.length; i++) {
      report.writeln('${i + 1}. ${issues[i]}');
    }
  } else {
    report.writeln('No issues found! 🎉');
  }
  report.writeln('');

  // Recommendations
  report.writeln('## 🚀 NEXT STEPS');
  if (issues.isNotEmpty) {
    report.writeln('1. Fix the issues listed above');
    report.writeln('2. Re-run the comprehensive test');
    report.writeln('3. Proceed with production deployment');
  } else {
    report.writeln('1. System is ready for production!');
    report.writeln('2. Consider performance optimizations');
    report.writeln('3. Set up monitoring and analytics');
  }

  // Save report
  final reportFile = File('COMPREHENSIVE_TEST_REPORT.md');
  await reportFile.writeAsString(report.toString());

  print('✅ Test report saved to: COMPREHENSIVE_TEST_REPORT.md');
  print(
      '📄 Report size: ${(await reportFile.length() / 1024).toStringAsFixed(1)}KB');

  // Print summary
  print('\n🎯 TEST SUMMARY:');
  print('📊 Tests Passed: $passedTests/$totalTests');
  print('🚨 Issues Found: ${issues.length}');
  print(
      '📋 Overall Status: ${passedTests == totalTests ? "✅ ALL TESTS PASSED" : "⚠️ NEEDS ATTENTION"}');
}
