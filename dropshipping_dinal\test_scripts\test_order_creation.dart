import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'lib/firebase_options.dart';
import 'lib/services/firebase_service.dart';
import 'lib/models/order_model.dart';

/// Test script to verify order creation works properly
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // Initialize Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    
    print('🔥 Firebase initialized successfully');
    
    // Test order creation
    await testOrderCreation();
    
  } catch (e) {
    print('❌ Error: $e');
  }
}

Future<void> testOrderCreation() async {
  print('🧪 Testing order creation...');
  
  try {
    // Create test order items
    final testItems = [
      OrderItem(
        productId: 'test_product_1',
        productName: 'Test Product',
        productImage: 'https://placehold.co/600x400.png',
        price: 100.0,
        mainPrice: 80.0,
        quantity: 1,
        color: 'Red',
        size: 'M',
      ),
    ];
    
    // Test order creation with timeout
    final orderId = await Future.any([
      firebaseService.createOrder(
        userName: 'Test User',
        userPhone: '1234567890',
        address: 'Test Address',
        city: 'Test City',
        items: testItems,
        totalAmount: 100.0,
        totalEarnings: 20.0,
      ),
      Future.delayed(
        const Duration(seconds: 10),
        () => throw Exception('Order creation timeout'),
      ),
    ]);
    
    print('✅ Order created successfully with ID: $orderId');
    print('🎉 Order creation test passed!');
    
  } catch (e) {
    print('❌ Order creation test failed: $e');
  }
}
