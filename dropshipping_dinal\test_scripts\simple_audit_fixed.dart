import 'dart:io';

/// SIMPLE SYSTEM AUDIT - WORKING VERSION
void main() async {
  print('🔍 SIMPLE SYSTEM AUDIT STARTING...\n');
  
  final issues = <String>[];

  try {
    // Check critical directories
    final criticalDirectories = [
      'lib/services',
      'lib/models', 
      'lib/pages',
      'lib/auth'
    ];

    print('📋 CHECKING DIRECTORY STRUCTURE...\n');
    for (String dirPath in criticalDirectories) {
      final dir = Directory(dirPath);
      final exists = await dir.exists();
      
      if (exists) {
        final fileCount = await dir.list().length;
        print('✅ $dirPath: $fileCount files');
      } else {
        print('❌ $dirPath: Missing');
        issues.add('Missing directory: $dirPath');
      }
    }

    // Check configuration files
    print('\n📋 CHECKING CONFIGURATION FILES...\n');
    final configFiles = [
      'pubspec.yaml',
      'lib/firebase_options.dart'
    ];

    for (String filePath in configFiles) {
      final file = File(filePath);
      if (await file.exists()) {
        print('✅ $filePath: Found');
      } else {
        print('❌ $filePath: Missing');
        issues.add('Missing file: $filePath');
      }
    }

    // Generate simple report
    print('\n📊 GENERATING REPORT...\n');
    final report = StringBuffer();
    report.writeln('# SIMPLE AUDIT REPORT');
    report.writeln('Generated: ${DateTime.now()}');
    report.writeln('');
    report.writeln('## ISSUES FOUND: ${issues.length}');
    
    if (issues.isNotEmpty) {
      for (int i = 0; i < issues.length; i++) {
        report.writeln('${i + 1}. ${issues[i]}');
      }
    } else {
      report.writeln('No issues found! 🎉');
    }

    // Save report
    final reportFile = File('SIMPLE_AUDIT_REPORT.md');
    await reportFile.writeAsString(report.toString());

    print('✅ Audit completed successfully!');
    print('📊 Issues found: ${issues.length}');
    print('📄 Report saved to: SIMPLE_AUDIT_REPORT.md');

  } catch (e) {
    print('❌ Audit failed: $e');
  }
}
