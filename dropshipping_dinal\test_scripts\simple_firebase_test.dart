import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:developer' as developer;

class SimpleFirebaseTest extends StatefulWidget {
  const SimpleFirebaseTest({super.key});

  @override
  State<SimpleFirebaseTest> createState() => _SimpleFirebaseTestState();
}

class _SimpleFirebaseTestState extends State<SimpleFirebaseTest> {
  String status = 'Testing...';
  List<Map<String, dynamic>> products = [];
  String? error;

  @override
  void initState() {
    super.initState();
    testFirebase();
  }

  Future<void> testFirebase() async {
    try {
      setState(() {
        status = 'Connecting to Firebase...';
        error = null;
      });

      developer.log('🔥 Starting simple Firebase test...');

      // Direct Firebase query - no services, no models
      final firestore = FirebaseFirestore.instance;
      
      setState(() {
        status = 'Querying products collection...';
      });

      final snapshot = await firestore.collection('products').get();
      
      developer.log('📊 Firebase query completed. Found ${snapshot.docs.length} documents');

      final List<Map<String, dynamic>> productList = [];
      
      for (var doc in snapshot.docs) {
        final data = doc.data();
        data['id'] = doc.id; // Add document ID
        productList.add(data);
        developer.log('📦 Product: ${data['name']} - Featured: ${data['featured']}');
      }

      setState(() {
        status = 'Success! Found ${productList.length} products';
        products = productList;
      });

    } catch (e) {
      developer.log('❌ Firebase test error: $e');
      setState(() {
        status = 'Error occurred';
        error = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🔥 Simple Firebase Test'),
        backgroundColor: const Color(0xFF96AB46),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🔥 Firebase Connection Test',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: error != null ? Colors.red.shade50 : Colors.blue.shade50,
                border: Border.all(
                  color: error != null ? Colors.red : Colors.blue,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Status: $status',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  if (error != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Error: $error',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ],
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            if (products.isNotEmpty) ...[
              Text(
                '📦 Products Found:',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: products.length,
                  itemBuilder: (context, index) {
                    final product = products[index];
                    return Card(
                      child: ListTile(
                        title: Text(product['name'] ?? 'No Name'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Price: \$${product['mainPrice'] ?? 'N/A'}'),
                            Text('Featured: ${product['featured'] ?? false}'),
                            Text('Flash Sale: ${product['flashSale'] ?? false}'),
                            Text('Category: ${product['category'] ?? 'N/A'}'),
                          ],
                        ),
                        trailing: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (product['featured'] == true)
                              const Icon(Icons.star, color: Colors.orange),
                            if (product['flashSale'] == true)
                              const Icon(Icons.flash_on, color: Colors.red),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: testFirebase,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF96AB46),
                foregroundColor: Colors.white,
              ),
              child: const Text('🔄 Test Again'),
            ),
          ],
        ),
      ),
    );
  }
}
