import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'lib/firebase_options.dart';

/// Complete Firebase structure setup and data migration
void main() async {
  print('🔥 FIREBASE COMPLETE SETUP STARTING...\n');

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  final firestore = FirebaseFirestore.instance;

  try {
    // Phase 1: Fix existing collections
    await fixExistingCollections(firestore);

    // Phase 2: Create missing collections
    await createMissingCollections(firestore);

    // Phase 3: Set up proper relationships
    await setupDataRelationships(firestore);

    print('\n🎉 FIREBASE SETUP COMPLETED SUCCESSFULLY!');
    print('\n📋 SUMMARY:');
    print('✅ Fixed products collection (categories, imageUrl)');
    print('✅ Enhanced orders collection (admin panel fields)');
    print('✅ Created userBalances collection');
    print('✅ Created earnings collection');
    print('✅ Created categories collection');
    print('✅ Created settings collection');
    print('✅ Set up proper admin system');
  } catch (e) {
    print('❌ Error: $e');
  }
}

/// Fix existing collections structure
Future<void> fixExistingCollections(FirebaseFirestore firestore) async {
  print('🔧 PHASE 1: Fixing existing collections...\n');

  // Fix products collection
  await fixProductsCollection(firestore);

  // Fix orders collection
  await fixOrdersCollection(firestore);

  // Fix users collection
  await fixUsersCollection(firestore);

  // Fix withdrawals collection
  await fixWithdrawalsCollection(firestore);
}

/// Fix products collection
Future<void> fixProductsCollection(FirebaseFirestore firestore) async {
  print('📦 Fixing products collection...');

  final snapshot = await firestore.collection('products').get();

  for (final doc in snapshot.docs) {
    final data = doc.data();
    final updates = <String, dynamic>{};

    // Fix imageUrl array to string
    if (data['imageUrl'] is List) {
      final imageList = data['imageUrl'] as List;
      updates['imageUrl'] =
          imageList.isNotEmpty ? imageList.first.toString() : '';
    }

    // Fix categories
    if (data['category'] == 'non' ||
        data['categories']?.contains('non') == true) {
      updates['category'] = 'Electronics';
      updates['categories'] = ['Electronics'];
    }

    // Add missing fields
    updates['primaryCategory'] =
        updates['category'] ?? data['category'] ?? 'Electronics';
    updates['updatedAt'] = FieldValue.serverTimestamp();

    await doc.reference.update(updates);
    print('  ✅ Fixed product: ${data['name']}');
  }
}

/// Fix orders collection
Future<void> fixOrdersCollection(FirebaseFirestore firestore) async {
  print('🛒 Fixing orders collection...');

  final snapshot = await firestore.collection('orders').get();

  for (final doc in snapshot.docs) {
    final data = doc.data();
    final updates = <String, dynamic>{};

    // Add admin panel compatibility fields
    updates['customerName'] = data['userName'] ?? 'Unknown Customer';
    updates['customerPhone'] = data['userPhone'] ?? '';
    updates['customerEmail'] = '';
    updates['shippingAddress'] =
        '${data['address'] ?? ''}, ${data['city'] ?? ''}';
    updates['notes'] = '';
    updates['trackingNumber'] = '';
    updates['updatedAt'] = FieldValue.serverTimestamp();

    await doc.reference.update(updates);
    print('  ✅ Fixed order: ${doc.id}');
  }
}

/// Fix users collection
Future<void> fixUsersCollection(FirebaseFirestore firestore) async {
  print('👥 Fixing users collection...');

  // Remove placeholder users
  final snapshot = await firestore.collection('users').get();
  for (final doc in snapshot.docs) {
    if (doc.data()['isPlaceholder'] == true) {
      await doc.reference.delete();
    }
  }

  print('  ✅ Cleaned up placeholder users');
}

/// Fix withdrawals collection
Future<void> fixWithdrawalsCollection(FirebaseFirestore firestore) async {
  print('💸 Fixing withdrawals collection...');

  final snapshot = await firestore.collection('withdrawals').get();

  for (final doc in snapshot.docs) {
    final data = doc.data();
    if (data['isPlaceholder'] == true) {
      await doc.reference.delete();
      continue;
    }

    final updates = <String, dynamic>{};
    updates['paymentMethod'] = 'bank_transfer';
    updates['paymentDetails'] = {};
    updates['notes'] = '';
    updates['updatedAt'] = FieldValue.serverTimestamp();

    await doc.reference.update(updates);
    print('  ✅ Fixed withdrawal: ${doc.id}');
  }
}

/// Create missing collections
Future<void> createMissingCollections(FirebaseFirestore firestore) async {
  print('\n🆕 PHASE 2: Creating missing collections...\n');

  // Create userBalances collection
  await createUserBalancesCollection(firestore);

  // Create earnings collection
  await createEarningsCollection(firestore);

  // Create categories collection
  await createCategoriesCollection(firestore);

  // Create settings collection
  await createSettingsCollection(firestore);
}

/// Create userBalances collection
Future<void> createUserBalancesCollection(FirebaseFirestore firestore) async {
  print('💰 Creating userBalances collection...');

  // Get all users from orders to create their balances
  final ordersSnapshot = await firestore.collection('orders').get();
  final userIds =
      ordersSnapshot.docs.map((doc) => doc.data()['userId']).toSet();

  for (final userId in userIds) {
    if (userId != null) {
      await firestore.collection('userBalances').doc(userId.toString()).set({
        'userId': userId,
        'availableBalance': 0.0,
        'incomingEarnings': 0.0,
        'totalEarnings': 0.0,
        'pendingWithdrawals': 0.0,
        'totalWithdrawn': 0.0,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      print('  ✅ Created balance for user: $userId');
    }
  }
}

/// Create earnings collection
Future<void> createEarningsCollection(FirebaseFirestore firestore) async {
  print('📈 Creating earnings collection...');

  // Create earnings records from existing orders
  final ordersSnapshot = await firestore.collection('orders').get();

  for (final orderDoc in ordersSnapshot.docs) {
    final orderData = orderDoc.data();

    await firestore.collection('earnings').add({
      'userId': orderData['userId'],
      'orderId': orderDoc.id,
      'amount': orderData['totalEarnings'] ?? 0.0,
      'status':
          orderData['earningsConfirmed'] == true ? 'confirmed' : 'pending',
      'orderStatus': orderData['status'] ?? 'pending',
      'createdAt': orderData['createdAt'] ?? FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  print('  ✅ Created earnings records from existing orders');
}

/// Create categories collection
Future<void> createCategoriesCollection(FirebaseFirestore firestore) async {
  print('🏷️ Creating categories collection...');

  final categories = [
    {'id': 'electronics', 'name': 'Electronics', 'icon': '📱', 'active': true},
    {'id': 'clothing', 'name': 'Clothing', 'icon': '👕', 'active': true},
    {'id': 'accessories', 'name': 'Accessories', 'icon': '⌚', 'active': true},
    {'id': 'home', 'name': 'Home & Garden', 'icon': '🏠', 'active': true},
    {'id': 'sports', 'name': 'Sports', 'icon': '⚽', 'active': true},
  ];

  for (final category in categories) {
    await firestore.collection('categories').doc(category['id'] as String).set({
      ...category,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });
    print('  ✅ Created category: ${category['name']}');
  }
}

/// Create settings collection
Future<void> createSettingsCollection(FirebaseFirestore firestore) async {
  print('⚙️ Creating settings collection...');

  await firestore.collection('settings').doc('app').set({
    'appName': 'Dropshipping Store',
    'appVersion': '1.0.0',
    'maintenanceMode': false,
    'minWithdrawalAmount': 50.0,
    'maxWithdrawalAmount': 5000.0,
    'withdrawalFee': 0.0,
    'socialMedia': {
      'instagram': 'https://instagram.com/yourstore',
      'telegram': 'https://t.me/yourstore',
      'facebook': 'https://facebook.com/yourstore',
    },
    'createdAt': FieldValue.serverTimestamp(),
    'updatedAt': FieldValue.serverTimestamp(),
  });

  print('  ✅ Created app settings');
}

/// Set up data relationships
Future<void> setupDataRelationships(FirebaseFirestore firestore) async {
  print('\n🔗 PHASE 3: Setting up data relationships...\n');

  // Calculate and update user balances from orders
  await calculateUserBalances(firestore);

  print('  ✅ Updated user balances from existing orders');
}

/// Calculate user balances from existing orders
Future<void> calculateUserBalances(FirebaseFirestore firestore) async {
  final ordersSnapshot = await firestore.collection('orders').get();
  final userEarnings = <String, double>{};

  // Calculate total earnings per user
  for (final orderDoc in ordersSnapshot.docs) {
    final orderData = orderDoc.data();
    final userId = orderData['userId'] as String?;
    final earnings = (orderData['totalEarnings'] ?? 0.0).toDouble();

    if (userId != null) {
      userEarnings[userId] = (userEarnings[userId] ?? 0.0) + earnings;
    }
  }

  // Update user balances
  for (final entry in userEarnings.entries) {
    await firestore.collection('userBalances').doc(entry.key).update({
      'incomingEarnings': entry.value,
      'totalEarnings': entry.value,
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }
}
