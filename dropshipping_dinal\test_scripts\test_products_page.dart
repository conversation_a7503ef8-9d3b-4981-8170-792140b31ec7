import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:developer' as developer;
import 'services/product_service.dart';
import 'dto/product_dto.dart';

class TestProductsPage extends StatefulWidget {
  const TestProductsPage({super.key});

  @override
  State<TestProductsPage> createState() => _TestProductsPageState();
}

class _TestProductsPageState extends State<TestProductsPage> {
  List<ProductListItemDTO> products = [];
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    loadProducts();
  }

  Future<void> loadProducts() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      developer.log('🔍 Starting product loading test...');

      final productService =
          Provider.of<ProductService>(context, listen: false);

      // Test 1: Try to get all products (no filters)
      developer.log('📋 Test 1: Getting all products...');
      final allProducts = await productService.getProducts();
      developer.log('📊 Raw products found: ${allProducts.length}');

      // Test 2: Try to get products for UI
      developer.log('📋 Test 2: Getting products for UI...');
      final loadedProducts = await productService.getProductsForUI(limit: 10);
      developer.log('📊 UI products found: ${loadedProducts.length}');

      // Test 3: Try direct Firebase query
      developer.log('📋 Test 3: Testing direct Firebase access...');
      try {
        final firestore = FirebaseFirestore.instance;
        final snapshot = await firestore.collection('products').get();
        developer.log(
            '📊 Direct Firebase query found: ${snapshot.docs.length} documents');

        if (snapshot.docs.isNotEmpty) {
          final firstDoc = snapshot.docs.first;
          developer.log('📦 First product data: ${firstDoc.data()}');
        }
      } catch (firebaseError) {
        developer.log('❌ Direct Firebase error: $firebaseError');
      }

      setState(() {
        products = loadedProducts;
        isLoading = false;
      });

      if (products.isEmpty) {
        setState(() {
          error = 'No products found. Check logs for details.';
        });
      }

      developer
          .log('🎉 Test completed. Found ${products.length} products for UI');
    } catch (e) {
      setState(() {
        error = 'Error: $e';
        isLoading = false;
      });
      developer.log('❌ Error loading products: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🔍 Products Test'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: loadProducts,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '📊 Product Test Results',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            if (isLoading)
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Loading products...'),
                  ],
                ),
              )
            else if (error != null)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  border: Border.all(color: Colors.red),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '❌ Error:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(error!),
                  ],
                ),
              )
            else if (products.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  border: Border.all(color: Colors.orange),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '⚠️ No Products Found',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                        'Your admin panel products are not appearing in Firebase.'),
                    Text('Check your admin panel Firebase configuration.'),
                  ],
                ),
              )
            else
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        border: Border.all(color: Colors.green),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '✅ Success!',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text('Found ${products.length} products'),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: ListView.builder(
                        itemCount: products.length,
                        itemBuilder: (context, index) {
                          final product = products[index];
                          return Card(
                            child: ListTile(
                              leading: product.imageUrl.isNotEmpty
                                  ? Image.network(
                                      product.imageUrl,
                                      width: 50,
                                      height: 50,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error,
                                              stackTrace) =>
                                          const Icon(Icons.image_not_supported),
                                    )
                                  : const Icon(Icons.shopping_bag),
                              title: Text(product.name),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Price: \$${product.price}'),
                                  Text(
                                      'Categories: ${product.categories.join(', ')}'),
                                  Text(
                                      'In Stock: ${product.inStock ? 'Yes' : 'No'}'),
                                ],
                              ),
                              trailing: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  if (product.featured)
                                    const Icon(Icons.star,
                                        color: Colors.orange),
                                  if (product.flashSale)
                                    const Icon(Icons.flash_on,
                                        color: Colors.red),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
