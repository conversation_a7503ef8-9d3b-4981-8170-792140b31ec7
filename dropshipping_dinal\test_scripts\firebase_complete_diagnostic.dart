import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';

/// Comprehensive Firebase Diagnostic and Testing Script
/// This script will:
/// 1. 🔍 Debug why orders aren't saving
/// 2. 📊 Show current Firebase collection status
/// 3. 🧪 Test the complete order flow
/// 4. 🔧 Fix any collection structure issues

void main() async {
  print('🔥 FIREBASE COMPLETE DIAGNOSTIC STARTING...\n');

  try {
    // Initialize Firebase
    await Firebase.initializeApp();
    print('✅ Firebase initialized successfully\n');

    final diagnostic = FirebaseDiagnostic();

    // Run all diagnostic tests
    await diagnostic.runCompleteAnalysis();
  } catch (e) {
    print('❌ Failed to initialize Firebase: $e');
    exit(1);
  }
}

class FirebaseDiagnostic {
  final FirebaseFirestore firestore = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  /// Run complete Firebase analysis
  Future<void> runCompleteAnalysis() async {
    print('🚀 STARTING COMPLETE FIREBASE ANALYSIS\n');
    print('=' * 60);

    // 1. Check Firebase connection and authentication
    await _checkFirebaseConnection();
    await _checkAuthentication();

    // 2. Analyze all collections
    await _analyzeCollections();

    // 3. Test order creation flow
    await _testOrderCreationFlow();

    // 4. Check and fix collection structures
    await _checkAndFixCollectionStructures();

    // 5. Test complete user flow
    await _testCompleteUserFlow();

    print('\n${'=' * 60}');
    print('🎉 COMPLETE FIREBASE ANALYSIS FINISHED');
    print('=' * 60);
  }

  /// 1. Check Firebase connection
  Future<void> _checkFirebaseConnection() async {
    print('🔍 1. CHECKING FIREBASE CONNECTION');
    print('-' * 40);

    try {
      // Test Firestore connection
      await firestore.collection('_test').limit(1).get();
      print('✅ Firestore connection: WORKING');

      // Check project info
      print('📋 Project ID: ${firestore.app.options.projectId}');
      print('📋 App Name: ${firestore.app.name}');
    } catch (e) {
      print('❌ Firestore connection: FAILED - $e');
    }
    print('');
  }

  /// 2. Check authentication status
  Future<void> _checkAuthentication() async {
    print('👤 2. CHECKING AUTHENTICATION STATUS');
    print('-' * 40);

    try {
      final currentUser = auth.currentUser;

      if (currentUser != null) {
        print('✅ User authenticated: ${currentUser.uid}');
        print('📧 Email: ${currentUser.email ?? 'No email'}');
        print('📱 Phone: ${currentUser.phoneNumber ?? 'No phone'}');
        print('✉️ Email verified: ${currentUser.emailVerified}');
      } else {
        print('❌ No user authenticated');
        print('⚠️  This is likely why orders aren\'t saving!');

        // Try to create a test user
        await _createTestUser();
      }
    } catch (e) {
      print('❌ Authentication check failed: $e');
    }
    print('');
  }

  /// 3. Analyze all collections
  Future<void> _analyzeCollections() async {
    print('📊 3. ANALYZING FIREBASE COLLECTIONS');
    print('-' * 40);

    final collections = [
      'products',
      'orders',
      'users',
      'carts',
      'earnings',
      'withdrawals',
      'userBalances'
    ];

    for (final collectionName in collections) {
      await _analyzeCollection(collectionName);
    }
    print('');
  }

  /// Analyze individual collection
  Future<void> _analyzeCollection(String collectionName) async {
    try {
      final snapshot = await firestore.collection(collectionName).get();
      print('📁 $collectionName: ${snapshot.docs.length} documents');

      if (snapshot.docs.isNotEmpty) {
        // Show sample document structure
        final sampleDoc = snapshot.docs.first;
        final data = sampleDoc.data();
        print('   📋 Sample fields: ${data.keys.join(', ')}');

        // Check for common issues
        if (collectionName == 'orders') {
          _checkOrdersStructure(snapshot.docs);
        } else if (collectionName == 'products') {
          _checkProductsStructure(snapshot.docs);
        }
      } else {
        print('   ⚠️  Collection is empty');
      }
    } catch (e) {
      print('❌ $collectionName: Error accessing - $e');
    }
  }

  /// 4. Test order creation flow
  Future<void> _testOrderCreationFlow() async {
    print('🧪 4. TESTING ORDER CREATION FLOW');
    print('-' * 40);

    try {
      // Check if user is authenticated
      if (auth.currentUser == null) {
        print('❌ Cannot test order creation - no authenticated user');
        return;
      }

      // Create test order data
      final testOrderData = {
        'userId': auth.currentUser!.uid,
        'userName': 'Test User',
        'userPhone': '1234567890',
        'address': 'Test Address',
        'city': 'Test City',
        'items': [
          {
            'productId': 'test_product_1',
            'productName': 'Test Product',
            'productImage': 'test_image.jpg',
            'price': 100.0,
            'mainPrice': 80.0,
            'quantity': 1,
            'color': 'Red',
          }
        ],
        'totalAmount': 100.0,
        'totalEarnings': 20.0,
        'status': 'pending',
        'earningsConfirmed': false,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // Try to create order
      print('🔄 Creating test order...');
      final orderRef = await firestore.collection('orders').add(testOrderData);
      print('✅ Test order created successfully: ${orderRef.id}');

      // Test earnings creation
      await _testEarningsCreation(orderRef.id, 20.0);

      // Test user balance update
      await _testUserBalanceUpdate(20.0);

      // Clean up test order
      await orderRef.delete();
      print('🧹 Test order cleaned up');
    } catch (e) {
      print('❌ Order creation test failed: $e');
      print('🔍 This is likely why orders aren\'t saving in your app!');
    }
    print('');
  }

  /// Test earnings creation
  Future<void> _testEarningsCreation(String orderId, double amount) async {
    try {
      final earningsData = {
        'userId': auth.currentUser!.uid,
        'orderId': orderId,
        'amount': amount,
        'status': 'pending',
        'orderStatus': 'pending',
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      final earningsRef =
          await firestore.collection('earnings').add(earningsData);
      print('✅ Test earnings created: ${earningsRef.id}');

      // Clean up
      await earningsRef.delete();
    } catch (e) {
      print('❌ Earnings creation failed: $e');
    }
  }

  /// Test user balance update
  Future<void> _testUserBalanceUpdate(double amount) async {
    try {
      final userBalanceRef =
          firestore.collection('userBalances').doc(auth.currentUser!.uid);

      await userBalanceRef.set({
        'userId': auth.currentUser!.uid,
        'availableBalance': 0.0,
        'incomingEarnings': FieldValue.increment(amount),
        'totalEarnings': FieldValue.increment(amount),
        'pendingWithdrawals': 0.0,
        'totalWithdrawn': 0.0,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      print('✅ User balance updated successfully');

      // Read back the balance
      final balanceDoc = await userBalanceRef.get();
      if (balanceDoc.exists) {
        final data = balanceDoc.data() as Map<String, dynamic>;
        print(
            '💰 Current incoming earnings: ${data['incomingEarnings'] ?? 0.0}');
      }
    } catch (e) {
      print('❌ User balance update failed: $e');
    }
  }

  /// 5. Check and fix collection structures
  Future<void> _checkAndFixCollectionStructures() async {
    print('🔧 5. CHECKING AND FIXING COLLECTION STRUCTURES');
    print('-' * 40);

    await _fixProductsCollection();
    await _fixOrdersCollection();
    await _fixUsersCollection();
    await _createMissingCollections();
    print('');
  }

  /// Fix products collection issues
  Future<void> _fixProductsCollection() async {
    try {
      print('🔄 Checking products collection...');
      final snapshot = await firestore.collection('products').get();

      int fixedCount = 0;
      for (final doc in snapshot.docs) {
        final data = doc.data();
        bool needsUpdate = false;
        Map<String, dynamic> updates = {};

        // Fix category field (should not be 'non')
        if (data['category'] == 'non' || data['categories'] == 'non') {
          updates['category'] = 'Electronics';
          updates['categories'] = ['Electronics'];
          needsUpdate = true;
        }

        // Fix imageUrl (should be array)
        if (data['imageUrl'] is String) {
          updates['imageUrl'] = [data['imageUrl']];
          needsUpdate = true;
        }

        // Ensure required pricing fields exist
        if (!data.containsKey('mainPrice')) {
          updates['mainPrice'] = (data['price'] ?? 100.0) * 0.8;
          needsUpdate = true;
        }

        if (!data.containsKey('minPrice')) {
          updates['minPrice'] =
              data['mainPrice'] ?? updates['mainPrice'] ?? 80.0;
          needsUpdate = true;
        }

        if (!data.containsKey('maxPrice')) {
          updates['maxPrice'] =
              (data['mainPrice'] ?? updates['mainPrice'] ?? 80.0) * 1.5;
          needsUpdate = true;
        }

        if (needsUpdate) {
          await doc.reference.update(updates);
          fixedCount++;
        }
      }

      print('✅ Products collection: Fixed $fixedCount documents');
    } catch (e) {
      print('❌ Products collection fix failed: $e');
    }
  }

  /// Fix orders collection
  Future<void> _fixOrdersCollection() async {
    try {
      print('🔄 Checking orders collection...');
      final snapshot = await firestore.collection('orders').get();

      print('📊 Orders collection: ${snapshot.docs.length} documents');

      // Check for missing fields in orders
      for (final doc in snapshot.docs) {
        final data = doc.data();
        Map<String, dynamic> updates = {};

        if (!data.containsKey('earningsConfirmed')) {
          updates['earningsConfirmed'] = false;
        }

        if (!data.containsKey('status')) {
          updates['status'] = 'pending';
        }

        if (!data.containsKey('updatedAt')) {
          updates['updatedAt'] = FieldValue.serverTimestamp();
        }

        if (updates.isNotEmpty) {
          await doc.reference.update(updates);
        }
      }

      print('✅ Orders collection structure verified');
    } catch (e) {
      print('❌ Orders collection check failed: $e');
    }
  }

  /// Fix users collection
  Future<void> _fixUsersCollection() async {
    try {
      print('🔄 Checking users collection...');
      final snapshot = await firestore.collection('users').get();

      print('📊 Users collection: ${snapshot.docs.length} documents');
      print('✅ Users collection structure verified');
    } catch (e) {
      print('❌ Users collection check failed: $e');
    }
  }

  /// Create missing collections
  Future<void> _createMissingCollections() async {
    final requiredCollections = [
      'earnings',
      'userBalances',
      'withdrawals',
      'carts'
    ];

    for (final collectionName in requiredCollections) {
      try {
        final snapshot =
            await firestore.collection(collectionName).limit(1).get();
        if (snapshot.docs.isEmpty) {
          print('⚠️  Creating missing collection: $collectionName');

          // Create a placeholder document
          await firestore.collection(collectionName).doc('_placeholder').set({
            'created': FieldValue.serverTimestamp(),
            'note': 'Placeholder document - can be deleted',
          });

          print('✅ Created collection: $collectionName');
        }
      } catch (e) {
        print('❌ Failed to create collection $collectionName: $e');
      }
    }
  }

  /// 6. Test complete user flow
  Future<void> _testCompleteUserFlow() async {
    print('🧪 6. TESTING COMPLETE USER FLOW');
    print('-' * 40);

    if (auth.currentUser == null) {
      print('❌ Cannot test user flow - no authenticated user');
      return;
    }

    try {
      // Test 1: Add item to cart
      await _testCartOperations();

      // Test 2: Create order
      await _testOrderCreation();

      // Test 3: Check earnings
      await _testEarningsFlow();

      print('✅ Complete user flow test passed');
    } catch (e) {
      print('❌ User flow test failed: $e');
    }
    print('');
  }

  /// Test cart operations
  Future<void> _testCartOperations() async {
    try {
      final cartRef = firestore.collection('carts').doc(auth.currentUser!.uid);

      final cartData = {
        'userId': auth.currentUser!.uid,
        'items': [
          {
            'productId': 'test_product',
            'name': 'Test Product',
            'imageUrl': 'test.jpg',
            'selectedPrice': 100.0,
            'mainPrice': 80.0,
            'quantity': 1,
            'color': 'Red',
          }
        ],
        'updatedAt': FieldValue.serverTimestamp(),
        'totalAmount': 100.0,
        'totalEarnings': 20.0,
        'itemCount': 1,
      };

      await cartRef.set(cartData);
      print('✅ Cart operations test passed');

      // Clean up
      await cartRef.delete();
    } catch (e) {
      print('❌ Cart operations test failed: $e');
    }
  }

  /// Test order creation
  Future<void> _testOrderCreation() async {
    try {
      final orderData = {
        'userId': auth.currentUser!.uid,
        'userName': 'Test User',
        'userPhone': '1234567890',
        'address': 'Test Address',
        'city': 'Test City',
        'items': [
          {
            'productId': 'test_product',
            'productName': 'Test Product',
            'productImage': 'test.jpg',
            'price': 100.0,
            'mainPrice': 80.0,
            'quantity': 1,
            'color': 'Red',
          }
        ],
        'totalAmount': 100.0,
        'totalEarnings': 20.0,
        'status': 'pending',
        'earningsConfirmed': false,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      final orderRef = await firestore.collection('orders').add(orderData);
      print('✅ Order creation test passed: ${orderRef.id}');

      // Clean up
      await orderRef.delete();
    } catch (e) {
      print('❌ Order creation test failed: $e');
    }
  }

  /// Test earnings flow
  Future<void> _testEarningsFlow() async {
    try {
      // Test earnings record creation
      final earningsData = {
        'userId': auth.currentUser!.uid,
        'orderId': 'test_order_123',
        'amount': 20.0,
        'status': 'pending',
        'orderStatus': 'pending',
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      final earningsRef =
          await firestore.collection('earnings').add(earningsData);
      print('✅ Earnings creation test passed');

      // Test user balance update
      final balanceRef =
          firestore.collection('userBalances').doc(auth.currentUser!.uid);
      await balanceRef.set({
        'userId': auth.currentUser!.uid,
        'availableBalance': 0.0,
        'incomingEarnings': 20.0,
        'totalEarnings': 20.0,
        'pendingWithdrawals': 0.0,
        'totalWithdrawn': 0.0,
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      print('✅ User balance update test passed');

      // Clean up
      await earningsRef.delete();
    } catch (e) {
      print('❌ Earnings flow test failed: $e');
    }
  }

  /// Create test user if none exists
  Future<void> _createTestUser() async {
    try {
      print('🔄 Creating test user...');

      final userCredential = await auth.createUserWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'testpassword123',
      );

      print('✅ Test user created: ${userCredential.user?.uid}');

      // Create user document
      await firestore.collection('users').doc(userCredential.user!.uid).set({
        'email': '<EMAIL>',
        'createdAt': FieldValue.serverTimestamp(),
        'role': 'user',
      });

      print('✅ User document created');
    } catch (e) {
      if (e.toString().contains('email-already-in-use')) {
        print('ℹ️  Test user already exists, trying to sign in...');
        try {
          await auth.signInWithEmailAndPassword(
            email: '<EMAIL>',
            password: 'testpassword123',
          );
          print('✅ Signed in with existing test user');
        } catch (signInError) {
          print('❌ Failed to sign in with test user: $signInError');
        }
      } else {
        print('❌ Failed to create test user: $e');
      }
    }
  }

  /// Check orders structure
  void _checkOrdersStructure(List<QueryDocumentSnapshot> docs) {
    if (docs.isEmpty) return;

    final sampleOrder = docs.first.data() as Map<String, dynamic>;
    final requiredFields = [
      'userId',
      'userName',
      'userPhone',
      'address',
      'city',
      'items',
      'totalAmount',
      'totalEarnings',
      'status',
      'earningsConfirmed'
    ];

    final missingFields = requiredFields
        .where((field) => !sampleOrder.containsKey(field))
        .toList();

    if (missingFields.isNotEmpty) {
      print('   ⚠️  Missing fields in orders: ${missingFields.join(', ')}');
    } else {
      print('   ✅ Orders structure is complete');
    }
  }

  /// Check products structure
  void _checkProductsStructure(List<QueryDocumentSnapshot> docs) {
    if (docs.isEmpty) return;

    final sampleProduct = docs.first.data() as Map<String, dynamic>;
    final requiredFields = [
      'name',
      'description',
      'mainPrice',
      'minPrice',
      'maxPrice',
      'imageUrl',
      'category',
      'inStock'
    ];

    final missingFields = requiredFields
        .where((field) => !sampleProduct.containsKey(field))
        .toList();

    if (missingFields.isNotEmpty) {
      print('   ⚠️  Missing fields in products: ${missingFields.join(', ')}');
    } else {
      print('   ✅ Products structure is complete');
    }

    // Check for specific issues
    if (sampleProduct['category'] == 'non') {
      print('   ⚠️  Products have category set to "non" - needs fixing');
    }

    if (sampleProduct['imageUrl'] is String) {
      print('   ⚠️  imageUrl should be array, not string - needs fixing');
    }
  }
}
