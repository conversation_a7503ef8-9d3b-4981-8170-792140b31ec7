import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';

/// Comprehensive System Audit Script
/// This script performs a thorough analysis of the entire dropshipping system
void main() async {
  print('🔍 COMPREHENSIVE SYSTEM AUDIT STARTING...\n');
  
  try {
    // Initialize Firebase
    await Firebase.initializeApp();
    print('✅ Firebase initialized successfully\n');
    
    // Run all audit phases
    await auditPhase1_SystemArchitecture();
    await auditPhase2_FlutterApp();
    await auditPhase3_FirebaseIntegration();
    await auditPhase4_AdminPanelConnectivity();
    await auditPhase5_DataConsistency();
    await auditPhase6_SecurityAnalysis();
    await auditPhase7_PerformanceAnalysis();
    
    print('\n🎉 COMPREHENSIVE AUDIT COMPLETED!');
    await generateAuditReport();
    
  } catch (e) {
    print('❌ Audit failed: $e');
  }
}

/// Phase 1: System Architecture Analysis
Future<void> auditPhase1_SystemArchitecture() async {
  print('📋 PHASE 1: SYSTEM ARCHITECTURE ANALYSIS\n');
  
  final firestore = FirebaseFirestore.instance;
  
  // Check all collections exist
  final collections = [
    'products', 'orders', 'users', 'carts', 
    'earnings', 'withdrawals', 'userBalances', 'admin'
  ];
  
  for (String collection in collections) {
    try {
      final snapshot = await firestore.collection(collection).limit(1).get();
      print('✅ Collection "$collection": ${snapshot.docs.isNotEmpty ? "Has data" : "Empty"}');
    } catch (e) {
      print('❌ Collection "$collection": Error - $e');
    }
  }
  print('');
}

/// Phase 2: Flutter App Analysis
Future<void> auditPhase2_FlutterApp() async {
  print('📋 PHASE 2: FLUTTER APP ANALYSIS\n');
  
  // Check critical files exist
  final criticalFiles = [
    'lib/services/firebase_service.dart',
    'lib/services/cart_service.dart',
    'lib/services/balance_service.dart',
    'lib/services/product_service.dart',
    'lib/models/product_model.dart',
    'lib/models/order_model.dart',
    'lib/models/user_model.dart',
    'lib/dto/product_dto.dart',
    'lib/auth/firebase_auth/firebase_auth_manager.dart',
  ];
  
  for (String filePath in criticalFiles) {
    final file = File(filePath);
    if (await file.exists()) {
      final size = await file.length();
      print('✅ $filePath: ${(size / 1024).toStringAsFixed(1)}KB');
    } else {
      print('❌ $filePath: Missing');
    }
  }
  print('');
}

/// Phase 3: Firebase Integration Analysis
Future<void> auditPhase3_FirebaseIntegration() async {
  print('📋 PHASE 3: FIREBASE INTEGRATION ANALYSIS\n');
  
  final firestore = FirebaseFirestore.instance;
  
  try {
    // Test products collection
    final productsSnapshot = await firestore.collection('products').limit(5).get();
    print('✅ Products collection: ${productsSnapshot.docs.length} documents');
    
    // Check product data structure
    if (productsSnapshot.docs.isNotEmpty) {
      final productData = productsSnapshot.docs.first.data();
      final hasImageUrl = productData.containsKey('imageUrl');
      final hasCategories = productData.containsKey('categories');
      final hasMainPrice = productData.containsKey('mainPrice');
      
      print('   - imageUrl field: ${hasImageUrl ? "✅" : "❌"}');
      print('   - categories field: ${hasCategories ? "✅" : "❌"}');
      print('   - mainPrice field: ${hasMainPrice ? "✅" : "❌"}');
      
      // Check for data issues
      if (productData['categories'] == 'non') {
        print('   ⚠️  Warning: Found category="non" (needs fixing)');
      }
      
      if (productData['imageUrl'] is List) {
        print('   ⚠️  Warning: imageUrl is array (should be string)');
      }
    }
    
    // Test orders collection
    final ordersSnapshot = await firestore.collection('orders').limit(5).get();
    print('✅ Orders collection: ${ordersSnapshot.docs.length} documents');
    
    // Test earnings collection
    final earningsSnapshot = await firestore.collection('earnings').limit(5).get();
    print('✅ Earnings collection: ${earningsSnapshot.docs.length} documents');
    
    // Test user balances
    final balancesSnapshot = await firestore.collection('userBalances').limit(5).get();
    print('✅ User balances collection: ${balancesSnapshot.docs.length} documents');
    
  } catch (e) {
    print('❌ Firebase integration error: $e');
  }
  print('');
}

/// Phase 4: Admin Panel Connectivity Analysis
Future<void> auditPhase4_AdminPanelConnectivity() async {
  print('📋 PHASE 4: ADMIN PANEL CONNECTIVITY ANALYSIS\n');
  
  // Test admin panel endpoints
  final endpoints = [
    'http://localhost:9002',
    'http://localhost:9002/api/products',
    'http://localhost:9002/api/orders',
    'http://localhost:9002/api/users',
  ];
  
  final client = HttpClient();
  
  for (String endpoint in endpoints) {
    try {
      final request = await client.getUrl(Uri.parse(endpoint));
      request.headers.set('Accept', 'application/json');
      final response = await request.close();
      
      if (response.statusCode == 200) {
        print('✅ $endpoint: Accessible');
      } else {
        print('⚠️  $endpoint: Status ${response.statusCode}');
      }
    } catch (e) {
      print('❌ $endpoint: Not accessible - $e');
    }
  }
  
  client.close();
  print('');
}

/// Phase 5: Data Consistency Analysis
Future<void> auditPhase5_DataConsistency() async {
  print('📋 PHASE 5: DATA CONSISTENCY ANALYSIS\n');
  
  final firestore = FirebaseFirestore.instance;
  
  try {
    // Check for orphaned data
    final ordersSnapshot = await firestore.collection('orders').get();
    final earningsSnapshot = await firestore.collection('earnings').get();
    
    int ordersWithEarnings = 0;
    int earningsWithoutOrders = 0;
    
    // Check if all orders have corresponding earnings
    for (var orderDoc in ordersSnapshot.docs) {
      final orderId = orderDoc.id;
      final earningsQuery = await firestore
          .collection('earnings')
          .where('orderId', isEqualTo: orderId)
          .get();
      
      if (earningsQuery.docs.isNotEmpty) {
        ordersWithEarnings++;
      }
    }
    
    // Check if all earnings have corresponding orders
    for (var earningDoc in earningsSnapshot.docs) {
      final orderId = earningDoc.data()['orderId'];
      if (orderId != null) {
        final orderDoc = await firestore.collection('orders').doc(orderId).get();
        if (!orderDoc.exists) {
          earningsWithoutOrders++;
        }
      }
    }
    
    print('✅ Orders with earnings: $ordersWithEarnings/${ordersSnapshot.docs.length}');
    print('${earningsWithoutOrders > 0 ? "⚠️" : "✅"} Orphaned earnings: $earningsWithoutOrders');
    
  } catch (e) {
    print('❌ Data consistency check failed: $e');
  }
  print('');
}

/// Phase 6: Security Analysis
Future<void> auditPhase6_SecurityAnalysis() async {
  print('📋 PHASE 6: SECURITY ANALYSIS\n');
  
  // Check if security rules file exists
  final rulesFile = File('firestore.rules');
  if (await rulesFile.exists()) {
    print('✅ Firestore security rules file exists');
    
    final rulesContent = await rulesFile.readAsString();
    final hasAdminCheck = rulesContent.contains('isAdmin()');
    final hasUserCheck = rulesContent.contains('isOwner(');
    final hasAuth = rulesContent.contains('request.auth');
    
    print('   - Admin role checking: ${hasAdminCheck ? "✅" : "❌"}');
    print('   - User ownership checking: ${hasUserCheck ? "✅" : "❌"}');
    print('   - Authentication required: ${hasAuth ? "✅" : "❌"}');
  } else {
    print('❌ Firestore security rules file missing');
  }
  print('');
}

/// Phase 7: Performance Analysis
Future<void> auditPhase7_PerformanceAnalysis() async {
  print('📋 PHASE 7: PERFORMANCE ANALYSIS\n');
  
  final firestore = FirebaseFirestore.instance;
  
  try {
    // Test query performance
    final stopwatch = Stopwatch()..start();
    
    // Test product query
    await firestore.collection('products').limit(10).get();
    final productQueryTime = stopwatch.elapsedMilliseconds;
    
    stopwatch.reset();
    
    // Test order query
    await firestore.collection('orders').limit(10).get();
    final orderQueryTime = stopwatch.elapsedMilliseconds;
    
    stopwatch.stop();
    
    print('✅ Product query time: ${productQueryTime}ms');
    print('✅ Order query time: ${orderQueryTime}ms');
    
    if (productQueryTime > 2000) {
      print('⚠️  Warning: Product queries are slow (>2s)');
    }
    
    if (orderQueryTime > 2000) {
      print('⚠️  Warning: Order queries are slow (>2s)');
    }
    
  } catch (e) {
    print('❌ Performance analysis failed: $e');
  }
  print('');
}

/// Generate comprehensive audit report
Future<void> generateAuditReport() async {
  print('📊 GENERATING COMPREHENSIVE AUDIT REPORT...\n');
  
  final report = StringBuffer();
  report.writeln('# COMPREHENSIVE SYSTEM AUDIT REPORT');
  report.writeln('Generated: ${DateTime.now()}');
  report.writeln('');
  report.writeln('## SYSTEM STATUS SUMMARY');
  report.writeln('- Architecture: Analyzed');
  report.writeln('- Flutter App: Analyzed');
  report.writeln('- Firebase Integration: Analyzed');
  report.writeln('- Admin Panel: Analyzed');
  report.writeln('- Data Consistency: Analyzed');
  report.writeln('- Security: Analyzed');
  report.writeln('- Performance: Analyzed');
  
  // Save report to file
  final reportFile = File('SYSTEM_AUDIT_REPORT.md');
  await reportFile.writeAsString(report.toString());
  
  print('✅ Audit report saved to: SYSTEM_AUDIT_REPORT.md');
}
