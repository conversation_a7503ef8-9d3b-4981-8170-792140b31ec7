import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'lib/firebase_options.dart';

/// COMPLETE FIREBASE SETUP AND DATA MIGRATION
/// This script will:
/// 1. Fix all existing data issues
/// 2. Create missing collections
/// 3. Set up proper relationships
/// 4. Ensure admin panel compatibility

void main() async {
  print('🔥 STARTING COMPLETE FIREBASE SETUP...\n');

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  final firestore = FirebaseFirestore.instance;

  try {
    print('📋 PHASE 1: FIXING EXISTING DATA...\n');
    await fixExistingData(firestore);

    print('\n📋 PHASE 2: CREATING MISSING COLLECTIONS...\n');
    await createMissingCollections(firestore);

    print('\n📋 PHASE 3: SETTING UP RELATIONSHIPS...\n');
    await setupDataRelationships(firestore);

    print('\n🎉 COMPLETE SETUP FINISHED SUCCESSFULLY!');
    print('\n📊 SUMMARY OF CHANGES:');
    print(
        '✅ Fixed products: categories "non" → "Electronics", imageUrl arrays → strings');
    print('✅ Enhanced orders: added admin panel compatibility fields');
    print('✅ Created userBalances: tracks available/incoming/pending balances');
    print('✅ Created earnings: detailed earnings history');
    print('✅ Created categories: proper product categorization');
    print('✅ Created settings: app configuration');
    print('✅ Set up relationships: calculated user balances from orders');
  } catch (e) {
    print('❌ ERROR: $e');
  }
}

/// Fix existing data issues
Future<void> fixExistingData(FirebaseFirestore firestore) async {
  // Fix products collection
  print('📦 Fixing products collection...');
  final productsSnapshot = await firestore.collection('products').get();

  for (final doc in productsSnapshot.docs) {
    final data = doc.data();
    final updates = <String, dynamic>{};
    bool needsUpdate = false;

    // Fix imageUrl if it's an array
    if (data['imageUrl'] is List) {
      final imageList = data['imageUrl'] as List;
      if (imageList.isNotEmpty) {
        updates['imageUrl'] = imageList.first.toString();
        needsUpdate = true;
        print('  📸 Fixed imageUrl for: ${data['name']}');
      }
    }

    // Fix categories
    if (data['category'] == 'non' ||
        (data['categories'] as List?)?.contains('non') == true) {
      updates['category'] = 'Electronics';
      updates['categories'] = ['Electronics'];
      updates['primaryCategory'] = 'Electronics';
      needsUpdate = true;
      print('  🏷️ Fixed categories for: ${data['name']}');
    }

    // Ensure all required fields exist
    if (!data.containsKey('primaryCategory')) {
      updates['primaryCategory'] = data['category'] ?? 'Electronics';
      needsUpdate = true;
    }

    if (!data.containsKey('imageUrls') || data['imageUrls'] == null) {
      final imageUrl = updates['imageUrl'] ?? data['imageUrl'] ?? '';
      if (imageUrl.isNotEmpty) {
        updates['imageUrls'] = [imageUrl];
        needsUpdate = true;
      }
    }

    if (needsUpdate) {
      updates['updatedAt'] = FieldValue.serverTimestamp();
      await doc.reference.update(updates);
    }
  }

  // Fix orders collection
  print('🛒 Fixing orders collection...');
  final ordersSnapshot = await firestore.collection('orders').get();

  for (final doc in ordersSnapshot.docs) {
    final data = doc.data();
    final updates = <String, dynamic>{};

    // Add admin panel compatibility fields if missing
    if (!data.containsKey('customerName')) {
      updates['customerName'] = data['userName'] ?? 'Unknown Customer';
    }
    if (!data.containsKey('customerPhone')) {
      updates['customerPhone'] = data['userPhone'] ?? '';
    }
    if (!data.containsKey('customerEmail')) {
      updates['customerEmail'] = '';
    }
    if (!data.containsKey('shippingAddress')) {
      updates['shippingAddress'] =
          '${data['address'] ?? ''}, ${data['city'] ?? ''}';
    }
    if (!data.containsKey('notes')) {
      updates['notes'] = '';
    }
    if (!data.containsKey('trackingNumber')) {
      updates['trackingNumber'] = '';
    }
    if (!data.containsKey('updatedAt')) {
      updates['updatedAt'] = FieldValue.serverTimestamp();
    }

    if (updates.isNotEmpty) {
      await doc.reference.update(updates);
      print('  ✅ Fixed order: ${doc.id}');
    }
  }

  // Clean up placeholder documents
  print('🧹 Cleaning up placeholder documents...');

  // Remove placeholder users
  final usersSnapshot = await firestore.collection('users').get();
  for (final doc in usersSnapshot.docs) {
    if (doc.data()['isPlaceholder'] == true) {
      await doc.reference.delete();
      print('  🗑️ Removed placeholder user');
    }
  }

  // Remove placeholder withdrawals
  final withdrawalsSnapshot = await firestore.collection('withdrawals').get();
  for (final doc in withdrawalsSnapshot.docs) {
    if (doc.data()['isPlaceholder'] == true) {
      await doc.reference.delete();
      print('  🗑️ Removed placeholder withdrawal');
    }
  }

  // Remove placeholder admin
  final adminSnapshot = await firestore.collection('admin').get();
  for (final doc in adminSnapshot.docs) {
    if (doc.data()['isPlaceholder'] == true) {
      await doc.reference.delete();
      print('  🗑️ Removed placeholder admin');
    }
  }
}

/// Create missing collections
Future<void> createMissingCollections(FirebaseFirestore firestore) async {
  // Create categories collection
  print('🏷️ Creating categories collection...');
  final categories = [
    {
      'id': 'electronics',
      'name': 'Electronics',
      'icon': '📱',
      'active': true,
      'order': 1
    },
    {
      'id': 'clothing',
      'name': 'Clothing',
      'icon': '👕',
      'active': true,
      'order': 2
    },
    {
      'id': 'accessories',
      'name': 'Accessories',
      'icon': '⌚',
      'active': true,
      'order': 3
    },
    {
      'id': 'home',
      'name': 'Home & Garden',
      'icon': '🏠',
      'active': true,
      'order': 4
    },
    {'id': 'sports', 'name': 'Sports', 'icon': '⚽', 'active': true, 'order': 5},
  ];

  for (final category in categories) {
    await firestore.collection('categories').doc(category['id'] as String).set({
      ...category,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });
    print('  ✅ Created category: ${category['name']}');
  }

  // Create settings collection
  print('⚙️ Creating settings collection...');
  await firestore.collection('settings').doc('app').set({
    'appName': 'Dropshipping Store',
    'appVersion': '1.0.0',
    'maintenanceMode': false,
    'minWithdrawalAmount': 50.0,
    'maxWithdrawalAmount': 5000.0,
    'withdrawalFee': 0.0,
    'socialMedia': {
      'instagram': 'https://instagram.com/yourstore',
      'telegram': 'https://t.me/yourstore',
      'facebook': 'https://facebook.com/yourstore',
    },
    'theme': {
      'primaryColor': '#96ab46',
      'secondaryColor': '#ff9b24',
      'accentColor': '#fcf0e3',
      'darkColor': '#3b460d',
    },
    'createdAt': FieldValue.serverTimestamp(),
    'updatedAt': FieldValue.serverTimestamp(),
  });
  print('  ✅ Created app settings');
}

/// Set up data relationships
Future<void> setupDataRelationships(FirebaseFirestore firestore) async {
  print('🔗 Setting up data relationships...');

  // Get all orders to calculate user balances
  final ordersSnapshot = await firestore.collection('orders').get();
  final userEarnings = <String, Map<String, double>>{};

  // Calculate earnings per user
  for (final orderDoc in ordersSnapshot.docs) {
    final orderData = orderDoc.data();
    final userId = orderData['userId'] as String?;
    final totalEarnings = (orderData['totalEarnings'] ?? 0.0).toDouble();
    final earningsConfirmed = orderData['earningsConfirmed'] ?? false;

    if (userId != null) {
      if (!userEarnings.containsKey(userId)) {
        userEarnings[userId] = {
          'total': 0.0,
          'available': 0.0,
          'incoming': 0.0,
        };
      }

      userEarnings[userId]!['total'] =
          userEarnings[userId]!['total']! + totalEarnings;

      if (earningsConfirmed) {
        userEarnings[userId]!['available'] =
            userEarnings[userId]!['available']! + totalEarnings;
      } else {
        userEarnings[userId]!['incoming'] =
            userEarnings[userId]!['incoming']! + totalEarnings;
      }
    }

    // Create earnings record if it doesn't exist
    final earningsSnapshot = await firestore
        .collection('earnings')
        .where('orderId', isEqualTo: orderDoc.id)
        .get();

    if (earningsSnapshot.docs.isEmpty && userId != null) {
      await firestore.collection('earnings').add({
        'userId': userId,
        'orderId': orderDoc.id,
        'amount': totalEarnings,
        'status': earningsConfirmed ? 'confirmed' : 'pending',
        'orderStatus': orderData['status'] ?? 'pending',
        'createdAt': orderData['createdAt'] ?? FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      print('  📈 Created earnings record for order: ${orderDoc.id}');
    }
  }

  // Create/update user balances
  print('💰 Creating user balances...');
  for (final entry in userEarnings.entries) {
    final userId = entry.key;
    final earnings = entry.value;

    await firestore.collection('userBalances').doc(userId).set({
      'userId': userId,
      'availableBalance': earnings['available']!,
      'incomingEarnings': earnings['incoming']!,
      'totalEarnings': earnings['total']!,
      'pendingWithdrawals': 0.0,
      'totalWithdrawn': 0.0,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });
    print(
        '  ✅ Created balance for user: $userId (Total: \$${earnings['total']!.toStringAsFixed(2)})');
  }

  print('🔗 Data relationships setup completed!');
}
