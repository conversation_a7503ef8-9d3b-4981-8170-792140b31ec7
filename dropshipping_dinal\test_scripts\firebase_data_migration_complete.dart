// 🔥 COMPLETE FIREBASE DATA MIGRATION SCRIPT
// This script fixes all data structure inconsistencies in your Firebase database

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'lib/firebase_options.dart';

/// FIREBASE DATA MIGRATION - COMPLETE FIX
/// Estimated time: 2-3 hours
/// 
/// This script will:
/// 1. Fix imageUrl arrays to strings
/// 2. Convert category="non" to proper categories
/// 3. Standardize product data structure
/// 4. Add missing fields for admin panel compatibility
/// 5. Create data validation and backup

void main() async {
  print('🔥 STARTING COMPLETE FIREBASE DATA MIGRATION...\n');
  print('⏱️  Estimated time: 2-3 hours\n');
  
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  final firestore = FirebaseFirestore.instance;
  final migrationService = FirebaseDataMigrationService(firestore);

  try {
    // Step 1: Create backup
    print('📋 STEP 1: Creating data backup...');
    await migrationService.createDataBackup();
    
    // Step 2: Analyze current data
    print('\n🔍 STEP 2: Analyzing current data structure...');
    final analysis = await migrationService.analyzeDataStructure();
    migrationService.printAnalysisReport(analysis);
    
    // Step 3: Fix products collection
    print('\n🔧 STEP 3: Fixing products collection...');
    await migrationService.fixProductsCollection();
    
    // Step 4: Fix categories
    print('\n📂 STEP 4: Fixing categories...');
    await migrationService.fixCategoriesCollection();
    
    // Step 5: Validate migration
    print('\n✅ STEP 5: Validating migration...');
    await migrationService.validateMigration();
    
    // Step 6: Generate report
    print('\n📊 STEP 6: Generating migration report...');
    await migrationService.generateMigrationReport();
    
    print('\n🎉 FIREBASE DATA MIGRATION COMPLETED SUCCESSFULLY!');
    print('\n📋 SUMMARY:');
    print('✅ Data backup created');
    print('✅ Products collection fixed');
    print('✅ Categories standardized');
    print('✅ Image URLs converted to strings');
    print('✅ Admin panel compatibility ensured');
    print('✅ Migration validated');
    
  } catch (e) {
    print('\n❌ MIGRATION FAILED: $e');
    print('\n🔄 To restore from backup, run: dart restore_firebase_backup.dart');
  }
}

/// Firebase Data Migration Service
class FirebaseDataMigrationService {
  final FirebaseFirestore firestore;
  final Map<String, dynamic> migrationStats = {};
  
  FirebaseDataMigrationService(this.firestore);

  /// Create backup of current data
  Future<void> createDataBackup() async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final backupCollection = firestore.collection('_backup_$timestamp');
      
      // Backup products
      final productsSnapshot = await firestore.collection('products').get();
      final batch = firestore.batch();
      
      for (final doc in productsSnapshot.docs) {
        final backupDoc = backupCollection.doc('products_${doc.id}');
        batch.set(backupDoc, {
          'collection': 'products',
          'documentId': doc.id,
          'data': doc.data(),
          'backupTimestamp': FieldValue.serverTimestamp(),
        });
      }
      
      await batch.commit();
      print('  ✅ Backup created: ${productsSnapshot.docs.length} products backed up');
      
    } catch (e) {
      throw Exception('Failed to create backup: $e');
    }
  }

  /// Analyze current data structure
  Future<Map<String, dynamic>> analyzeDataStructure() async {
    final analysis = <String, dynamic>{
      'products': <String, dynamic>{},
      'issues': <String>[],
      'statistics': <String, dynamic>{},
    };

    try {
      final productsSnapshot = await firestore.collection('products').get();
      final products = productsSnapshot.docs;
      
      analysis['statistics']['totalProducts'] = products.length;
      
      int imageUrlArrayCount = 0;
      int categoryNonCount = 0;
      int missingFieldsCount = 0;
      final missingFields = <String>{};
      
      for (final doc in products) {
        final data = doc.data();
        
        // Check imageUrl format
        if (data['imageUrl'] is List) {
          imageUrlArrayCount++;
        }
        
        // Check category issues
        if (data['category'] == 'non' || 
            (data['categories'] is List && (data['categories'] as List).contains('non'))) {
          categoryNonCount++;
        }
        
        // Check required fields
        final requiredFields = [
          'name', 'description', 'mainPrice', 'minPrice', 'maxPrice',
          'imageUrl', 'category', 'inStock', 'createdAt'
        ];
        
        for (final field in requiredFields) {
          if (!data.containsKey(field) || data[field] == null) {
            missingFields.add(field);
            missingFieldsCount++;
          }
        }
      }
      
      analysis['statistics']['imageUrlArrays'] = imageUrlArrayCount;
      analysis['statistics']['categoryNonIssues'] = categoryNonCount;
      analysis['statistics']['missingFieldsCount'] = missingFieldsCount;
      analysis['statistics']['missingFields'] = missingFields.toList();
      
      // Add issues to fix
      if (imageUrlArrayCount > 0) {
        analysis['issues'].add('$imageUrlArrayCount products have imageUrl as array instead of string');
      }
      if (categoryNonCount > 0) {
        analysis['issues'].add('$categoryNonCount products have category="non"');
      }
      if (missingFieldsCount > 0) {
        analysis['issues'].add('$missingFieldsCount missing field instances across products');
      }
      
    } catch (e) {
      analysis['error'] = e.toString();
    }
    
    return analysis;
  }

  /// Print analysis report
  void printAnalysisReport(Map<String, dynamic> analysis) {
    final stats = analysis['statistics'] as Map<String, dynamic>;
    final issues = analysis['issues'] as List<String>;
    
    print('  📊 ANALYSIS RESULTS:');
    print('     Total products: ${stats['totalProducts']}');
    print('     ImageUrl arrays: ${stats['imageUrlArrays']}');
    print('     Category "non" issues: ${stats['categoryNonIssues']}');
    print('     Missing fields: ${stats['missingFieldsCount']}');
    
    if (issues.isNotEmpty) {
      print('\n  🚨 ISSUES TO FIX:');
      for (int i = 0; i < issues.length; i++) {
        print('     ${i + 1}. ${issues[i]}');
      }
    } else {
      print('  ✅ No issues found!');
    }
  }

  /// Fix products collection
  Future<void> fixProductsCollection() async {
    try {
      final productsSnapshot = await firestore.collection('products').get();
      final batch = firestore.batch();
      int fixedCount = 0;
      
      for (final doc in productsSnapshot.docs) {
        final data = doc.data();
        final updates = <String, dynamic>{};
        bool needsUpdate = false;
        
        // Fix imageUrl array to string
        if (data['imageUrl'] is List) {
          final imageList = data['imageUrl'] as List;
          if (imageList.isNotEmpty) {
            updates['imageUrl'] = imageList.first.toString();
            // Store additional images in additionalImages array
            if (imageList.length > 1) {
              updates['additionalImages'] = imageList.skip(1).map((e) => e.toString()).toList();
            }
          } else {
            updates['imageUrl'] = '';
          }
          needsUpdate = true;
          print('    🔧 Fixed imageUrl array for: ${data['name']}');
        }
        
        // Fix category="non"
        if (data['category'] == 'non') {
          updates['category'] = 'Electronics';
          updates['primaryCategory'] = 'Electronics';
          needsUpdate = true;
          print('    📂 Fixed category for: ${data['name']}');
        }
        
        // Fix categories array with "non"
        if (data['categories'] is List) {
          final categories = data['categories'] as List;
          if (categories.contains('non')) {
            updates['categories'] = ['Electronics'];
            needsUpdate = true;
            print('    📂 Fixed categories array for: ${data['name']}');
          }
        } else {
          // Add categories array if missing
          updates['categories'] = [data['category'] ?? 'Electronics'];
          needsUpdate = true;
        }
        
        // Add missing required fields
        if (!data.containsKey('primaryCategory')) {
          updates['primaryCategory'] = data['category'] ?? 'Electronics';
          needsUpdate = true;
        }
        
        if (!data.containsKey('inStock')) {
          updates['inStock'] = (data['stock'] ?? 0) > 0;
          needsUpdate = true;
        }
        
        if (!data.containsKey('featured')) {
          updates['featured'] = false;
          needsUpdate = true;
        }
        
        if (!data.containsKey('flashSale')) {
          updates['flashSale'] = false;
          needsUpdate = true;
        }
        
        // Add price field for admin panel compatibility
        if (!data.containsKey('price')) {
          updates['price'] = data['mainPrice'] ?? 0;
          needsUpdate = true;
        }
        
        // Add timestamps if missing
        if (!data.containsKey('createdAt')) {
          updates['createdAt'] = FieldValue.serverTimestamp();
          needsUpdate = true;
        }
        
        if (!data.containsKey('updatedAt')) {
          updates['updatedAt'] = FieldValue.serverTimestamp();
          needsUpdate = true;
        }
        
        // Add admin panel compatibility fields
        if (!data.containsKey('imageUrls')) {
          final imageUrls = <String>[];
          if (updates.containsKey('imageUrl') && updates['imageUrl'].isNotEmpty) {
            imageUrls.add(updates['imageUrl']);
          } else if (data['imageUrl'] != null && data['imageUrl'].isNotEmpty) {
            imageUrls.add(data['imageUrl'].toString());
          }
          if (updates.containsKey('additionalImages')) {
            imageUrls.addAll(List<String>.from(updates['additionalImages']));
          } else if (data['additionalImages'] != null) {
            imageUrls.addAll(List<String>.from(data['additionalImages']));
          }
          updates['imageUrls'] = imageUrls;
          needsUpdate = true;
        }
        
        if (needsUpdate) {
          batch.update(doc.reference, updates);
          fixedCount++;
        }
      }
      
      if (fixedCount > 0) {
        await batch.commit();
        print('  ✅ Fixed $fixedCount products');
      } else {
        print('  ✅ No products needed fixing');
      }
      
      migrationStats['productsFixed'] = fixedCount;
      
    } catch (e) {
      throw Exception('Failed to fix products collection: $e');
    }
  }

  /// Fix categories collection
  Future<void> fixCategoriesCollection() async {
    try {
      // Create standard categories if they don't exist
      final standardCategories = [
        {
          'id': 'electronics',
          'name': 'Electronics',
          'description': 'Electronic devices and gadgets',
          'imageUrl': 'https://images.unsplash.com/photo-1498049794561-7780e7231661?q=80&w=1000',
          'featured': true,
          'productCount': 0,
        },
        {
          'id': 'clothing',
          'name': 'Clothing',
          'description': 'Fashion and apparel',
          'imageUrl': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=1000',
          'featured': true,
          'productCount': 0,
        },
        {
          'id': 'home',
          'name': 'Home & Garden',
          'description': 'Home improvement and garden supplies',
          'imageUrl': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?q=80&w=1000',
          'featured': true,
          'productCount': 0,
        },
        {
          'id': 'sports',
          'name': 'Sports & Outdoors',
          'description': 'Sports equipment and outdoor gear',
          'imageUrl': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=1000',
          'featured': false,
          'productCount': 0,
        },
        {
          'id': 'beauty',
          'name': 'Beauty & Health',
          'description': 'Beauty products and health supplements',
          'imageUrl': 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?q=80&w=1000',
          'featured': false,
          'productCount': 0,
        },
      ];
      
      final batch = firestore.batch();
      int categoriesCreated = 0;
      
      for (final category in standardCategories) {
        final categoryRef = firestore.collection('categories').doc(category['id'] as String);
        final categoryDoc = await categoryRef.get();
        
        if (!categoryDoc.exists) {
          batch.set(categoryRef, {
            ...category,
            'createdAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });
          categoriesCreated++;
          print('    📂 Created category: ${category['name']}');
        }
      }
      
      if (categoriesCreated > 0) {
        await batch.commit();
        print('  ✅ Created $categoriesCreated categories');
      } else {
        print('  ✅ All categories already exist');
      }
      
      migrationStats['categoriesCreated'] = categoriesCreated;
      
    } catch (e) {
      throw Exception('Failed to fix categories collection: $e');
    }
  }

  /// Validate migration
  Future<void> validateMigration() async {
    try {
      final productsSnapshot = await firestore.collection('products').get();
      final validationResults = <String, dynamic>{
        'totalProducts': productsSnapshot.docs.length,
        'validProducts': 0,
        'issues': <String>[],
      };
      
      for (final doc in productsSnapshot.docs) {
        final data = doc.data();
        bool isValid = true;
        
        // Check imageUrl is string
        if (data['imageUrl'] is! String) {
          validationResults['issues'].add('${doc.id}: imageUrl is not a string');
          isValid = false;
        }
        
        // Check category is not "non"
        if (data['category'] == 'non') {
          validationResults['issues'].add('${doc.id}: category is still "non"');
          isValid = false;
        }
        
        // Check required fields exist
        final requiredFields = ['name', 'description', 'mainPrice', 'minPrice', 'maxPrice'];
        for (final field in requiredFields) {
          if (!data.containsKey(field) || data[field] == null) {
            validationResults['issues'].add('${doc.id}: missing field $field');
            isValid = false;
          }
        }
        
        if (isValid) {
          validationResults['validProducts']++;
        }
      }
      
      final validPercentage = (validationResults['validProducts'] / validationResults['totalProducts'] * 100).round();
      
      print('  📊 VALIDATION RESULTS:');
      print('     Total products: ${validationResults['totalProducts']}');
      print('     Valid products: ${validationResults['validProducts']}');
      print('     Success rate: $validPercentage%');
      
      if (validationResults['issues'].isNotEmpty) {
        print('     Issues found: ${validationResults['issues'].length}');
        for (final issue in validationResults['issues']) {
          print('       - $issue');
        }
      } else {
        print('  ✅ All products are valid!');
      }
      
      migrationStats['validationResults'] = validationResults;
      
    } catch (e) {
      throw Exception('Failed to validate migration: $e');
    }
  }

  /// Generate migration report
  Future<void> generateMigrationReport() async {
    final report = StringBuffer();
    final timestamp = DateTime.now().toIso8601String();
    
    report.writeln('# 🔥 FIREBASE DATA MIGRATION REPORT');
    report.writeln('**Generated:** $timestamp');
    report.writeln('');
    
    report.writeln('## 📊 MIGRATION STATISTICS');
    report.writeln('- **Products Fixed:** ${migrationStats['productsFixed'] ?? 0}');
    report.writeln('- **Categories Created:** ${migrationStats['categoriesCreated'] ?? 0}');
    
    if (migrationStats.containsKey('validationResults')) {
      final validation = migrationStats['validationResults'] as Map<String, dynamic>;
      report.writeln('- **Total Products:** ${validation['totalProducts']}');
      report.writeln('- **Valid Products:** ${validation['validProducts']}');
      report.writeln('- **Success Rate:** ${(validation['validProducts'] / validation['totalProducts'] * 100).round()}%');
    }
    
    report.writeln('');
    report.writeln('## ✅ FIXES APPLIED');
    report.writeln('1. **ImageUrl Arrays → Strings:** Converted array imageUrl fields to single string values');
    report.writeln('2. **Category "non" → "Electronics":** Fixed products with invalid category values');
    report.writeln('3. **Missing Fields:** Added required fields for admin panel compatibility');
    report.writeln('4. **Standard Categories:** Created standard category collection');
    report.writeln('5. **Timestamps:** Added createdAt and updatedAt timestamps');
    report.writeln('');
    
    report.writeln('## 🎯 ADMIN PANEL COMPATIBILITY');
    report.writeln('- ✅ ImageUrl format standardized');
    report.writeln('- ✅ Categories properly structured');
    report.writeln('- ✅ All required fields present');
    report.writeln('- ✅ Price fields compatible');
    report.writeln('- ✅ Timestamp fields added');
    
    print('  📄 Migration report generated');
    print('\n📋 MIGRATION REPORT:');
    print(report.toString());
  }
}
